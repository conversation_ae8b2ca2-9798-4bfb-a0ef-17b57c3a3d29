// @ts-check
import { test, expect } from '@playwright/test';
import DateTimeFormat from '../src/utils/date-time-format.js';

// Mock navigator for Node.js environment
const mockNavigator = (language, languages) => {
  global.navigator = {
    language,
    languages: languages || [language],
  };
};

test.describe('DateTimeFormat locale combination behavior', () => {
  test('should use app language with user region when different', () => {
    mockNavigator('en-SG');

    const testDate = new Date('2024-01-15T10:30:00Z');

    // Test with Chinese app locale and Singapore user
    const currentYear = new Date().getFullYear();
    const dtf = DateTimeFormat('zh-CN', {
      // Show year if not current year
      year: testDate.getFullYear() === currentYear ? undefined : 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    });

    const formatted = dtf.format(testDate);

    expect(global.navigator.language).toBe('en-SG');
    expect(formatted).toBeTruthy();
    // Should try zh-SG (Chinese language + Singapore region)
  });

  test('should respect user region preferences', () => {
    mockNavigator('en-GB');

    const testDate = new Date('2024-01-15T10:30:00Z');

    // Test with US English app locale and UK user
    const currentYear = new Date().getFullYear();
    const dtf = DateTimeFormat('en-US', {
      // Show year if not current year
      year: testDate.getFullYear() === currentYear ? undefined : 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    });

    const formatted = dtf.format(testDate);

    expect(global.navigator.language).toBe('en-GB');
    expect(formatted).toBeTruthy();
    // Should try en-GB (British formatting) instead of en-US
  });

  test('should handle different formatting options', () => {
    const testDate = new Date('2024-01-15T10:30:00Z');

    const currentYear = new Date().getFullYear();

    const withTime = DateTimeFormat('en-US', {
      // Show year if not current year
      year: testDate.getFullYear() === currentYear ? undefined : 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    }).format(testDate);

    const withoutTime = DateTimeFormat('en-US', {
      // Show year if not current year
      year: testDate.getFullYear() === currentYear ? undefined : 'numeric',
      month: 'short',
      day: 'numeric',
      // Hide time
      hour: undefined,
      minute: undefined,
    }).format(testDate);

    const customFormat = DateTimeFormat('en-US', {
      // Show year if not current year
      year: testDate.getFullYear() === currentYear ? undefined : 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      weekday: 'long',
    }).format(testDate);

    expect(withTime).toBeTruthy();
    expect(withoutTime).toBeTruthy();
    expect(customFormat).toBeTruthy();

    expect(typeof withTime).toBe('string');
    expect(typeof withoutTime).toBe('string');
    expect(typeof customFormat).toBe('string');
  });

  test('should fallback gracefully for unsupported locales', () => {
    const testDate = new Date('2024-01-15T10:30:00Z');

    // Test with unsupported locale
    const currentYear = new Date().getFullYear();
    const dtf = DateTimeFormat('xx-XX', {
      // Show year if not current year
      year: testDate.getFullYear() === currentYear ? undefined : 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    });

    const formatted = dtf.format(testDate);

    expect(typeof formatted).toBe('string');
    expect(formatted).toBeTruthy();
    // Should fallback to browser default and still work
  });

  test('should use en-SG when navigator.languages is ["en-SG", "en"] and app locale is en-US', () => {
    mockNavigator('en-SG', ['en-SG', 'en']);

    const testDate = new Date('2024-01-15T10:30:00Z');

    // Test with US English app locale and Singapore user
    const currentYear = new Date().getFullYear();
    const dtf = DateTimeFormat('en-US', {
      // Show year if not current year
      year: testDate.getFullYear() === currentYear ? undefined : 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    });

    const formatted = dtf.format(testDate);

    expect(global.navigator.language).toBe('en-SG');
    expect(global.navigator.languages).toEqual(['en-SG', 'en']);
    expect(formatted).toBeTruthy();
    // Should use en-SG (app language 'en' + user region 'SG')
  });
});
