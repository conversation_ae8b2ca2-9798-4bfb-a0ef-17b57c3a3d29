// @ts-check
import { test, expect } from '@playwright/test';

test.describe('DateTimeFormat locale combination behavior', () => {
  // Helper function to mock navigator properties
  const mockNavigator = async (page, language, languages) => {
    await page.addInitScript(
      (lang, langs) => {
        Object.defineProperty(navigator, 'language', {
          get: () => lang,
          configurable: true,
        });
        if (langs) {
          Object.defineProperty(navigator, 'languages', {
            get: () => langs,
            configurable: true,
          });
        }
      },
      language,
      languages,
    );
  };

  test('should use app language with user region when different', async ({
    page,
  }) => {
    await mockNavigator(page, 'en-SG');
    await page.goto('/');

    const result = await page.evaluate(async () => {
      const { default: DateTimeFormat } = await import(
        '/src/utils/date-time-format.js'
      );

      const testDate = new Date('2024-01-15T10:30:00Z');

      // Test with Chinese app locale and Singapore user
      const currentYear = new Date().getFullYear();
      const dtf = DateTimeFormat('zh-CN', {
        // Show year if not current year
        year: testDate.getFullYear() === currentYear ? undefined : 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
      });

      const formatted = dtf.format(testDate);

      return {
        formatted,
        userLocale: navigator.language,
      };
    });

    expect(result.userLocale).toBe('en-SG');
    expect(result.formatted).toBeTruthy();
    // Should try zh-SG (Chinese language + Singapore region)
  });

  test('should respect user region preferences', async ({ page }) => {
    await mockNavigator(page, 'en-GB');
    await page.goto('/');

    const result = await page.evaluate(async () => {
      const { default: DateTimeFormat } = await import(
        '/src/utils/date-time-format.js'
      );

      const testDate = new Date('2024-01-15T10:30:00Z');

      // Test with US English app locale and UK user
      const currentYear = new Date().getFullYear();
      const dtf = DateTimeFormat('en-US', {
        // Show year if not current year
        year: testDate.getFullYear() === currentYear ? undefined : 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
      });

      const formatted = dtf.format(testDate);

      return {
        formatted,
        userLocale: navigator.language,
      };
    });

    expect(result.userLocale).toBe('en-GB');
    expect(result.formatted).toBeTruthy();
    // Should try en-GB (British formatting) instead of en-US
  });

  test('should handle different formatting options', async ({ page }) => {
    await page.goto('/');

    const result = await page.evaluate(async () => {
      const { default: DateTimeFormat } = await import(
        '/src/utils/date-time-format.js'
      );

      const testDate = new Date('2024-01-15T10:30:00Z');

      const currentYear = new Date().getFullYear();

      const withTime = DateTimeFormat('en-US', {
        // Show year if not current year
        year: testDate.getFullYear() === currentYear ? undefined : 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
      }).format(testDate);

      const withoutTime = DateTimeFormat('en-US', {
        // Show year if not current year
        year: testDate.getFullYear() === currentYear ? undefined : 'numeric',
        month: 'short',
        day: 'numeric',
        // Hide time
        hour: undefined,
        minute: undefined,
      }).format(testDate);

      const customFormat = DateTimeFormat('en-US', {
        // Show year if not current year
        year: testDate.getFullYear() === currentYear ? undefined : 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        weekday: 'long',
      }).format(testDate);

      return {
        withTime,
        withoutTime,
        customFormat,
      };
    });

    expect(result.withTime).toBeTruthy();
    expect(result.withoutTime).toBeTruthy();
    expect(result.customFormat).toBeTruthy();

    expect(typeof result.withTime).toBe('string');
    expect(typeof result.withoutTime).toBe('string');
    expect(typeof result.customFormat).toBe('string');
  });

  test('should fallback gracefully for unsupported locales', async ({
    page,
  }) => {
    await page.goto('/');

    const result = await page.evaluate(async () => {
      const { default: DateTimeFormat } = await import(
        '/src/utils/date-time-format.js'
      );

      const testDate = new Date('2024-01-15T10:30:00Z');

      // Test with unsupported locale
      const currentYear = new Date().getFullYear();
      const dtf = DateTimeFormat('xx-XX', {
        // Show year if not current year
        year: testDate.getFullYear() === currentYear ? undefined : 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
      });

      const formatted = dtf.format(testDate);

      return {
        formatted,
        isString: typeof formatted === 'string',
      };
    });

    expect(result.isString).toBe(true);
    expect(result.formatted).toBeTruthy();
    // Should fallback to browser default and still work
  });

  test('should use en-SG when navigator.languages is ["en-SG", "en"] and app locale is en-US', async ({
    page,
  }) => {
    // Set up navigator mocking before going to the page
    await page.addInitScript(() => {
      Object.defineProperty(navigator, 'language', {
        get: () => 'en-SG',
        configurable: true,
      });
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-SG', 'en'],
        configurable: true,
      });
    });

    await page.goto('/');

    const result = await page.evaluate(async () => {
      const { default: DateTimeFormat } = await import(
        '/src/utils/date-time-format.js'
      );

      const testDate = new Date('2024-01-15T10:30:00Z');

      // Test with US English app locale and Singapore user
      const currentYear = new Date().getFullYear();
      const dtf = DateTimeFormat('en-US', {
        // Show year if not current year
        year: testDate.getFullYear() === currentYear ? undefined : 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
      });

      const formatted = dtf.format(testDate);

      return {
        formatted,
        userLanguage: navigator.language,
        userLanguages: navigator.languages,
      };
    });

    expect(result.userLanguage).toBe('en-SG');
    expect(result.userLanguages).toEqual(['en-SG', 'en']);
    expect(result.formatted).toBeTruthy();
    // Should use en-SG (app language 'en' + user region 'SG')
  });
});
