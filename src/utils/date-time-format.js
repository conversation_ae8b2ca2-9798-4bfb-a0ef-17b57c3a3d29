import localeMatch from './locale-match';
import mem from './mem';

const getLocales = () => {
  const locales = [...navigator.languages];
  try {
    const dtfLocale = new Intl.DateTimeFormat().resolvedOptions().locale;
    if (!locales.includes(dtfLocale)) {
      locales.push(dtfLocale);
    }
  } catch {}
  return locales;
};

const createLocale = mem((language, options = undefined) => {
  try {
    return new Intl.Locale(language, options);
  } catch {
    // Fallback to simple string splitting
    // May not work properly due to how complicated this is
    if (!language) return null;

    const parts = language.split('-');
    const fallbackLocale = {
      language: parts[0],
      region: parts[1] || null,
      script: null,
      numberingSystem: null,
      calendar: null,
      toString: () => language,
    };

    // If options provided, override with those values
    if (options) {
      return {
        ...fallbackLocale,
        ...options,
        toString: () => {
          const lang = options.language || fallbackLocale.language;
          const region = options.region || fallbackLocale.region;
          return region ? `${lang}-${region}` : lang;
        },
      };
    }

    return fallbackLocale;
  }
});

const _DateTimeFormat = (locale, opts) => {
  const options = opts;

  // Helper to try creating DateTimeFormat with a locale
  const tryLocale = (loc) => {
    try {
      return new Intl.DateTimeFormat(loc, options);
    } catch {
      return null;
    }
  };

  const locales = getLocales();
  const userLocale = createLocale(locales[0]);
  const appLocale = createLocale(locale);
  const userRegion = userLocale?.region;

  const userRegionLocale =
    userRegion && appLocale && appLocale.region !== userRegion
      ? createLocale(appLocale.language, {
          ...appLocale,
          region: userRegion,
        })?.toString()
      : null;

  const matchedLocale = localeMatch(
    [userRegionLocale, locale, locale?.replace(/-[a-z]+$/i, '')],
    locales,
    locale,
  );

  return tryLocale(matchedLocale) || tryLocale(undefined);
};

const DateTimeFormat = mem(_DateTimeFormat);

export default DateTimeFormat;
